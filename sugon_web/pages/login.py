from sugon_web.common.base import Base
from playwright.sync_api import Locator, Page


class LoginPage(Base):
    # ipt_name: Locator = None  # 初始化时动态绑定
    # ipt_pwd: Locator = None
    # btn_login: Locator = None

    def __init__(self, page, env):
        super().__init__(page, env)  # 调用父类构造方法
        self._init_locators()       # 初始化元素定位器

    # 页面元素定位与封装
    def _init_locators(self):
        """动态绑定页面元素定位器（避免硬编码）"""
        self.ipt_name = self.get_by_placeholder("请输入登录账号")
        self.ipt_pwd = self.get_by_placeholder("请输入登录密码")
        self.btn_login = self.locator('"登 录"')

    # 页面API操作级别的封装
    def login(self, name: str, pwd: str):
        """执行登录操作"""
        self.ipt_name.fill(name)
        self.ipt_pwd.fill(pwd)
        self.btn_login.click()

    def is_logged_in(self):
        """检查是否已登录（通过检查是否存在登录表单来判断）"""
        try:
            # 如果能找到登录表单，说明未登录
            self.ipt_name.wait_for(timeout=2000)
            return False
        except:
            # 找不到登录表单，说明已登录
            return True

    def ensure_login(self, name: str, pwd: str):
        """确保已登录，如果未登录则执行登录"""
        if not self.is_logged_in():
            self.login(name, pwd)

