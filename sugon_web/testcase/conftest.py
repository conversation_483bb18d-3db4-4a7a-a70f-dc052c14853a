import os
import pytest
from datetime import datetime
from playwright.sync_api import sync_playwright
from sugon_web.pages.cce import CCEPage
from sugon_web.pages.login import LoginPage
from sugon_web.pages.volume import EvsPage
import allure


def pytest_addoption(parser):
    """添加命令行参数"""
    parser.addoption("--host", action="store", default='************', help="测试环境管理VIP")
    parser.addoption("--headless", action="store", default="false", help="是否无头模式运行（true/false）")
    parser.addoption("--browser-type", action="store", default="chromium", help="浏览器类型（chromium/firefox/webkit）")


@pytest.fixture(scope="session")
def env(pytestconfig):
    """根据--host参数加载对应环境的配置"""
    host = pytestconfig.getoption("--host")
    env = {
        'url': f"https://{host}:30000",
        "username": "admin",
        "password": "keystone_sugon"
    }
    yield env


@pytest.fixture(scope="function")
def page(env, pytestconfig):
    """创建新页面，支持动态浏览器类型和 headless 模式"""
    browser_type = pytestconfig.getoption("--browser-type")
    headless = pytestconfig.getoption("--headless").lower() == "true"

    # 校验浏览器类型是否有效
    valid_browsers = ["chromium", "firefox", "webkit"]
    if browser_type not in valid_browsers:
        raise ValueError(f"无效的浏览器类型: {browser_type}。支持的选项: {valid_browsers}")

    with sync_playwright() as p:
        # 动态选择浏览器类型
        browser = getattr(p, browser_type).launch(
            headless=headless,
            slow_mo=0
        )
        context = browser.new_context(ignore_https_errors=True)  # 显式设置忽略 SSL 错误
        page = context.new_page()
        page.goto(env['url'])
        yield page
        context.close()
        browser.close()


@pytest.fixture(scope="function")
def login_page(page, env):
    """初始化登录页对象"""
    return LoginPage(page, env)


@pytest.fixture(scope="function")
def cce_page(page, env):
    """初始化CCE页对象"""
    return CCEPage(page, env)


@pytest.fixture(scope="function")
def evs_page(page, env):
    """初始化云硬盘页对象"""
    return EvsPage(page, env)


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    outcome = yield
    rep = outcome.get_result()
    if rep.when == "call" and rep.failed:
        page = item.funcargs.get("page", None)
        if page:
            try:
                # 生成截图
                screenshot_dir = "screenshots"
                os.makedirs(screenshot_dir, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join(
                    screenshot_dir, f"{item.name}_{timestamp}.png"
                )
                
                # 保存截图到文件
                page.screenshot(path=screenshot_path)
                
                # 将截图添加到 Allure 报告
                with open(screenshot_path, "rb") as f:
                    allure.attach(
                        body=f.read(),
                        name=f"失败截图_{item.name}",
                        attachment_type=allure.attachment_type.PNG
                    )
                
                # 添加失败时的页面信息
                allure.attach(
                    body=f"测试失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                         f"测试名称: {item.name}\n"
                         f"当前页面URL: {page.url}",
                    name="失败信息",
                    attachment_type=allure.attachment_type.TEXT
                )
                
                print(f"失败截图已保存并添加到 Allure 报告: {screenshot_path}")
                
            except Exception as e:
                print(f"截图保存失败: {e}")

