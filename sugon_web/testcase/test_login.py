import time
from sugon_web.common.assertion import assert_popup_text
from playwright.sync_api import expect


def test_login_error(login_page):
    """用户登录-页面输入框提示文本验证"""

    login_page.login("", "")
    expect(login_page.get_by_text("请输入密码")).to_be_visible()
    expect(login_page.get_by_text("请填写用户名称")).to_be_visible()
    login_page.login("admin", "keystone")
    expect(login_page.get_by_text("请输入密码")).not_to_be_visible()
    expect(login_page.get_by_text("请填写用户名称")).not_to_be_visible()


def test_login_user_error(login_page):
    """用户登录失败场景-用户名输入错误"""

    # 步骤1：输入错误账号密码并提交
    login_page.login("wrong_user", "wrong_pwd")

    # 步骤2：结果断言
    assert_popup_text(login_page.pop_message(), "用户名/密码错误")


def test_login_pwd_error(login_page):
    """用户登录失败场景-密码输入错误，且错误次数超过默认限制"""
    for attempt in range(1, 8):
        login_page.login("admin", f"wrong_pwd_{attempt}")
        msg = login_page.pop_message()
        if attempt < 5:
            assert_popup_text(msg, f"10分钟内，用户名/密码连续错误5次将被锁定1分钟，已错误{attempt}次")
        elif attempt == 5:
            assert_popup_text(msg, "用户名/密码错误次数达到上限，锁定1分钟")
            time.sleep(55)
        elif attempt == 6:
            assert_popup_text(msg, "用户名/密码错误次数达到上限，锁定1分钟")
            time.sleep(65)
        else:
            assert_popup_text(msg, "10分钟内，用户名/密码连续错误5次将被锁定1分钟，已错误1次")

