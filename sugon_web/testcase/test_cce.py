from playwright.sync_api import expect
from sugon_web.common.assertion import assert_status, assert_table_contains_keyword


def test_cce_create(cce_page):
    """测试CCE集群创建"""

    cce_page.click("集群管理")

    name = "cce-test"
    cce_page.cce_create(name, vpc="rxs-vpc-1")

    assert_status(cce_page, name,  timeout=30000)

def test_cce_search(cce_page):
    """页面列表搜索结果验证"""

    cce_page.click("集群管理")

    keyword = "cce-test"
    cce_page.search(keyword)

    assert_table_contains_keyword(cce_page, keyword)