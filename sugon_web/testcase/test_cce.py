from playwright.sync_api import expect
from sugon_web.common.assertion import assert_status, assert_table_contains_keyword


def test_cce_create(cce_page):
    """测试CCE集群创建"""

    cce_page.click("集群管理")

    name = "cce-test"
    cce_page.cce_create(name, vpc="rxs-vpc-1")

    assert_status(cce_page, name,  timeout=30000)

def test_cce_search(multi_service_pages):
    """页面列表搜索结果验证 - 测试CCE和EVS两个页面"""

    # 创建CCE页面并执行搜索
    cce_page = multi_service_pages('cce')
    cce_page.click("集群管理")

    keyword = "cce-test"
    cce_page.search(keyword)

    assert_table_contains_keyword(cce_page, keyword)

    # 创建EVS页面（在同一个浏览器会话中）
    evs_page = multi_service_pages('evs')
    # 这里可以添加EVS页面的相关操作
    # 例如：evs_page.click("云硬盘管理")
    print("成功访问了CCE和EVS两个页面")